# Homework 1 - Open Source Data Engineering Tools

This homework serves two purposes:

1. provide the student an opportunity to learn more about data engineering tools of their interest,

2. introduce the student to the workflow used to submit assignments in this class.

[Here is a link](https://virginiacommonwealth.instructure.com/courses/122236/assignments/1241133) to the homework assignment in Canvas.

Your report should be stored in the ``./report`` folder.  Any assets (PNG, JPG, etc.) should be stored underneath in ``./report/assets``.

FURTHER, you must modify this README to make it your own:

* GITHUB provides [good explanations of README.md'(https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-readmes) files,

* Here is a list of example *awesome* README files: <https://github.com/matiassingers/awesome-readme>

Part of your score for this assignment is how well you update/modify this README.

## Files and Folders

*./src* - contains the scaffold QMD file homework 1. Your job is to modify this document and render it to *html*.  The *html* file will
be created in the same *./src* folder.

*./samples* - contains several QMD files. Your job is to render these files and exam the output.  Output for these files will be in the *./docs* folder.

*./docs* - contains resulting files from quarto render operations.  *You should NEVER directly store files in ./docs.* Rather, you should let Quarto maintain the contents of the *./docs* folder using settings in the quarto project file, *_quarto.yml*.

