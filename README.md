# CMSC 408 - Data Engineering Tool Review

## Overview

This repository contains my analysis and review of open source data engineering tools for CMSC 408 (Data Engineering) at Virginia Commonwealth University. The project explores the modern data engineering landscape, categorizes various tools, and provides detailed analysis of selected tool categories.

## Assignment Objectives

This assignment serves multiple learning objectives:

1. **Tool Familiarity**: Install and configure development tools (VS Code, Git, Quarto) required for the course
2. **Data Engineering Understanding**: Analyze the nine major categories of data engineering tools from the 2025 landscape
3. **Critical Analysis**: Deep dive into three specific tool categories of personal interest
4. **Technical Documentation**: Practice creating professional reports using Quarto and Markdown
5. **Workflow Management**: Learn the submission process for future assignments

## Project Structure

The project is based on <PERSON><PERSON><PERSON>'s comprehensive overview of the [2025 Open Source Data Engineering Landscape](https://www.pracdata.io/p/open-source-data-engineering-landscape-2025), which organizes data engineering tools into nine major categories and numerous subcategories.

## Submission Process

This assignment follows a two-step submission process:
1. **Gradescope**: Submit GitHub repository for automated testing and grading
2. **Canvas**: Upload final HTML report for human review and evaluation

## Repository Structure

```
├── README.md                 # This file - project overview and documentation
├── Makefile                  # Build automation and cleanup commands
├── reports/                  # Main assignment files
│   ├── report.qmd           # Primary assignment document (Quarto Markdown)
│   ├── _quarto.yml          # Quarto configuration file
│   └── assets/              # Images and other media files
│       ├── tools-2024.webp # Data engineering landscape diagram (2024)
│       └── tools-2025.webp # Data engineering landscape diagram (2025)
└── samples/                 # Example Quarto files for learning
    ├── index.qmd            # Sample index file
    ├── gantt.qmd            # Gantt chart example
    ├── mermaid.qmd          # Mermaid diagram example
    ├── graphviz.qmd         # Graphviz diagram example
    └── _quarto.yml          # Sample Quarto configuration
```

## Getting Started

### Prerequisites

Before working on this assignment, ensure you have the following tools installed:

- **Git**: Version control system (already installed)
- **VS Code**: Text editor with Quarto extension
- **Quarto**: Document rendering system for .qmd files

### Installation Instructions

1. **Install Quarto**: Download from [quarto.org](https://quarto.org/docs/get-started/)
2. **Install VS Code**: Download from [code.visualstudio.com](https://code.visualstudio.com/)
3. **Install Quarto VS Code Extension**: Search for "Quarto" in VS Code extensions

### Building the Report

1. Edit the `reports/report.qmd` file with your content
2. Render the report using: `quarto render reports/report.qmd`
3. The HTML output will be generated in the same directory

## Assignment Requirements

The final report must include:

- **Tool Landscape Overview**: Summary of all nine major data engineering categories
- **Detailed Analysis**: In-depth examination of three chosen subcategories
- **Personal Reflection**: Thoughts on the assignment experience and learning outcomes
- **Professional Formatting**: Clean, well-structured HTML output

## Resources

- [Quarto Documentation](https://quarto.org/docs/guide/)
- [Markdown Basics](https://quarto.org/docs/authoring/markdown-basics.html)
- [VS Code Quarto Extension](https://quarto.org/docs/tools/vscode.html)
- [Original Article](https://www.pracdata.io/p/open-source-data-engineering-landscape-2025)

