---
title: Homework 1 - Open Source Data Engineering Tools
author:
      name: <PERSON><PERSON>
      email: <EMAIL>
date: 8/31/2025

## Useful references:
# - Basic Markdown: https://quarto.org/docs/authoring/markdown-basics.html
# - Quarto figures: https://quarto.org/docs/authoring/figures.html
# - HTML document basics: https://quarto.org/docs/output-formats/html-basics.html
# - Quarto guide: https://quarto.org/docs/guide/
# - VS Code and Quarto: https://quarto.org/docs/tools/vscode.html
#   (RTFM and GET THE EXTENSION!!!!)
---

[Link to project repository](https://github.com/cmsc-vcu/cmsc408-fa2025-hw1-SunayDharamsi)

## Introduction

This report provides a comprehensive analysis of the open source data engineering landscape as of 2025. The purpose of this document is to explore and categorize the diverse ecosystem of tools that power modern data engineering workflows, from data storage and processing to analytics and visualization. Through this analysis, I aim to understand how these tools work together to create robust data pipelines and identify areas of particular interest for future learning and application in data engineering projects.

# Open Source Data Engineering Tools

Author <PERSON><PERSON><PERSON> offers a nice overview of the [2025 data engineering landscape](https://www.pracdata.io/p/open-source-data-engineering-landscape-2025) in the on-line web site [Practical Data Engineering Substack](https://practicaldataengineering.substack.com/).

![](assets/tools-2025.webp)

# Major Categories

Mr. Sadeghi proposals nine major tools categories.

## Storage Systems

Storage systems are foundational components in data engineering that handle
the storage and retrieval of data. These systems can include traditional
databases, distributed storage solutions, and modern cloud-based storage 
options. They provide scalable, reliable, and secure environments to store 
structured, semi-structured, and unstructured data, ensuring data is
accessible for processing, analysis, and other operations.

## Data Lake Platform

Data Lake platforms are designed to store vast amounts of raw data in its native format until it is needed. Unlike traditional databases, data lakes can store structured, semi-structured, and unstructured data, providing a flexible and scalable storage solution. These platforms support the integration of multiple data sources, making it easier to perform big data analytics and machine learning on large datasets.

## Data Integration

Data Integration tools are essential for connecting and combining data from multiple sources into a unified view. These tools handle the extraction, transformation, and loading (ETL) processes that move data between systems, ensuring data quality and consistency across different platforms. Modern data integration solutions support both batch and real-time processing, enabling organizations to create comprehensive data pipelines that can handle diverse data formats and sources, from traditional databases to cloud services and streaming platforms.

## Data Processing and Computation

Data Processing and Computation tools provide the computational power needed to transform, analyze, and derive insights from large datasets. These systems include distributed computing frameworks, stream processing engines, and batch processing tools that can scale horizontally across clusters of machines. They enable complex data transformations, aggregations, and analytics workloads that would be impossible to perform on traditional single-machine systems, supporting everything from simple data cleaning to advanced machine learning model training.

## Workflow and DataOps

Workflow and DataOps tools focus on orchestrating, monitoring, and managing data pipelines and workflows. These platforms provide scheduling, dependency management, and error handling capabilities that ensure data processes run reliably and efficiently. They incorporate DevOps principles into data engineering, enabling version control, automated testing, and continuous integration/deployment for data pipelines, while providing visibility into pipeline performance and data lineage.

## Data Infrastructure and Monitoring

Data Infrastructure and Monitoring tools provide the foundational layer for observing, managing, and optimizing data systems. These tools offer real-time monitoring of data pipelines, infrastructure health, and performance metrics, enabling proactive identification and resolution of issues. They include logging systems, metrics collection platforms, alerting mechanisms, and infrastructure management tools that ensure data systems remain reliable, performant, and available for business-critical operations.

## ML/AI Platform

ML/AI Platform tools bridge the gap between data engineering and machine learning by providing integrated environments for model development, training, and deployment. These platforms offer features like experiment tracking, model versioning, automated machine learning (AutoML), and model serving capabilities. They enable data scientists and engineers to collaborate effectively, streamline the machine learning lifecycle, and deploy models at scale while maintaining proper governance and monitoring.

## Metadata Management

Metadata Management tools focus on cataloging, organizing, and governing data assets across an organization. These systems provide data discovery capabilities, lineage tracking, and governance frameworks that help users understand what data is available, where it comes from, and how it can be used. They play a crucial role in data governance, compliance, and enabling self-service analytics by making data assets more discoverable and understandable to business users.

## Analytics and Visualization

Analytics and Visualization tools enable users to explore, analyze, and present data insights through interactive dashboards, reports, and visualizations. These platforms range from business intelligence tools for executive reporting to advanced analytics environments for data scientists. They provide intuitive interfaces for data exploration, support various chart types and visualization techniques, and often include features for collaborative analysis and sharing of insights across organizations.

# Digging into the details

In the following sections I identify three subcategories of data engineering tools of greatest interest to me.



## Stream Processing Engines

**Stream Processing Engines** are specialized systems designed to process continuous streams of data in real-time as it arrives, rather than processing data in batches. These engines enable organizations to react to events and changes in data immediately, making them essential for applications requiring low-latency responses and real-time analytics.

### What Stream Processing Engines Do:

Stream processing engines consume data from various sources (such as message queues, IoT devices, web applications, or databases) and apply transformations, aggregations, and analytics operations on the data as it flows through the system. They can handle operations like filtering, joining multiple streams, windowing (grouping data by time intervals), and complex event processing.

### Why Stream Processing is Important:

In today's fast-paced digital environment, the ability to process and respond to data in real-time provides significant competitive advantages. Stream processing enables immediate fraud detection in financial transactions, real-time personalization in e-commerce, instant alerting for system monitoring, and live analytics for business intelligence. This immediate processing capability is crucial for applications where delayed responses can result in lost opportunities or increased risks.

### How Stream Processing Differs from Relational OLTP Databases:

While relational OLTP databases are designed for storing and retrieving structured data with strong consistency guarantees, stream processing engines focus on continuous data flow and transformation. OLTP systems handle discrete transactions with ACID properties, whereas stream processors handle unbounded data streams with eventual consistency. Stream processors are optimized for throughput and low latency rather than storage durability, and they typically don't maintain persistent state in the same way traditional databases do.

### Why This Category Interests Me:

Stream processing represents the cutting edge of real-time data engineering, which is increasingly important in modern applications. I'm particularly interested in how these systems handle the challenges of processing infinite data streams while maintaining fault tolerance and exactly-once processing guarantees. The ability to build responsive, event-driven applications that can react to data changes instantly opens up exciting possibilities for creating more intelligent and adaptive systems.

### Examples of Open-Source Stream Processing Engines:

**Apache Kafka Streams:** A lightweight library for building real-time streaming applications that integrate seamlessly with Apache Kafka.

**Apache Flink:** A powerful stream processing framework that provides low-latency processing with strong consistency guarantees.

**Apache Storm:** A distributed real-time computation system for processing streams of data with guaranteed message processing.

## NoSQL Document Databases

**NoSQL Document Databases** are non-relational database systems that store data in flexible, JSON-like document formats rather than traditional rows and columns. These databases are designed to handle semi-structured and unstructured data, providing schema flexibility and horizontal scalability that traditional relational databases often struggle to achieve.

### What Document Databases Do:

Document databases store data as documents (typically in JSON, BSON, or XML format) that can contain nested structures, arrays, and varying fields. They allow for dynamic schemas where different documents in the same collection can have different structures. These databases provide powerful querying capabilities for document content, support indexing on any field, and often include features like full-text search and geospatial queries.

### Why Document Databases are Important:

Modern applications often deal with complex, evolving data structures that don't fit well into rigid relational schemas. Document databases provide the flexibility needed for rapid application development, especially in agile environments where data models frequently change. They excel at storing user profiles, product catalogs, content management, and any scenario where data structures are hierarchical or vary significantly between records.

### How Document Databases Differ from Relational OLTP Databases:

Unlike relational databases that enforce strict schemas and normalize data across multiple tables, document databases embrace denormalization and schema flexibility. While OLTP databases use SQL and require predefined table structures, document databases typically use query languages designed for document structures and allow schema evolution without migrations. Document databases often sacrifice some ACID guarantees for improved performance and scalability, though many modern implementations provide configurable consistency levels.

### Why This Category Interests Me:

Document databases represent a paradigm shift in how we think about data modeling and storage. I'm fascinated by how they enable more natural data representations that closely match application object models, reducing the impedance mismatch between application code and database storage. The ability to rapidly prototype and iterate on data models without complex migrations makes them particularly appealing for modern development practices.

### Examples of Open-Source Document Databases:

**MongoDB:** The most popular document database, offering rich querying capabilities and horizontal scaling through sharding.

**CouchDB:** A document database that emphasizes ease of use and provides built-in replication and conflict resolution.

**Amazon DocumentDB:** A managed document database service that's compatible with MongoDB APIs.

## Data Orchestration Tools

**Data Orchestration Tools** are platforms that manage, schedule, and coordinate complex data workflows and pipelines. These tools provide the framework for defining dependencies between tasks, handling failures, and ensuring that data processing jobs execute in the correct order and at the right time.

### What Data Orchestration Tools Do:

Data orchestration tools allow data engineers to define workflows as directed acyclic graphs (DAGs) where each node represents a task and edges represent dependencies. They handle task scheduling, retry logic, error handling, and provide monitoring and alerting capabilities. These tools can coordinate diverse tasks including data extraction, transformation, machine learning model training, and data quality checks across different systems and environments.

### Why Data Orchestration is Important:

As data pipelines become more complex and involve multiple systems, manual coordination becomes impossible. Orchestration tools provide reliability, visibility, and maintainability for data workflows. They ensure that data dependencies are respected, failures are handled gracefully, and teams can collaborate effectively on complex data processing pipelines. This automation is essential for maintaining data quality and meeting SLAs in production environments.

### How Data Orchestration Differs from Relational OLTP Databases:

While OLTP databases focus on storing and retrieving data with transactional consistency, orchestration tools focus on coordinating processes and workflows. OLTP systems handle data operations, whereas orchestration tools handle process operations. Orchestration tools are concerned with temporal dependencies, scheduling, and workflow state management rather than data storage and retrieval. They complement databases by providing the control layer that determines when and how data operations should occur.

### Why This Category Interests Me:

Data orchestration is the nervous system of modern data infrastructure, and I'm intrigued by how these tools enable the creation of reliable, maintainable data pipelines at scale. The challenge of coordinating complex workflows while providing clear visibility and debugging capabilities represents an interesting intersection of distributed systems, workflow management, and user experience design. Understanding orchestration is crucial for building robust data platforms that can evolve with business needs.

### Examples of Open-Source Data Orchestration Tools:

**Apache Airflow:** The most popular open-source workflow orchestration platform with a rich ecosystem of operators and integrations.

**Prefect:** A modern workflow orchestration tool that emphasizes ease of use and provides advanced features like dynamic workflows.

**Dagster:** A data orchestrator that focuses on data assets and provides strong typing and testing capabilities for data pipelines.


# Reflection

## What I Liked About This Project

I particularly enjoyed the comprehensive overview of the data engineering landscape, as it provided a structured way to understand how different tools fit together in modern data infrastructure. The research process helped me discover tools I hadn't encountered before and understand their specific roles in the data engineering ecosystem. The combination of technical learning with practical tool setup gave me hands-on experience with the workflow I'll be using throughout the course.

## What I Found Most Challenging

The most challenging aspect was setting up the development environment, particularly installing and configuring Quarto and ensuring all the tools work together properly. Understanding the relationships between different categories of tools and how they complement each other required significant research and synthesis. The technical writing aspect was also demanding, as I needed to explain complex concepts clearly while maintaining accuracy and depth.

## What Surprised Me Most

I was surprised by the sheer breadth and diversity of tools available in the open-source data engineering ecosystem. The rapid evolution of the field, with new categories like stream processing and modern data orchestration tools, showed how quickly the landscape is changing. I was also surprised by how interconnected these tools are and how they form complete data platforms when combined effectively.

## How I Would Approach This Differently Next Time

Next time, I would start by installing all the required tools first before diving into the content creation, as the technical setup took longer than expected. I would also create a more structured research plan, perhaps using a spreadsheet to track different tool categories and their characteristics before writing. Additionally, I would allocate more time for the reflection process, as it proved valuable for consolidating my learning and identifying areas for future exploration.

# README

A quality README is an important part of EVERY project. Using the Quarto *include* command we're including a copy of your README in the project report so that a human can evaluate it.

Make sure that you edit the README so that it's explanatory!  Note that you don't need a readme within the *reports* folder for this assignment. We're only
focused on the root *README.md*.

[Here is some info](https://www.freecodecamp.org/news/how-to-write-a-good-readme-file/) on how to write a good README!

::: {style="background:lightgray; margin-left:20px; border-top: 3px solid black; border-bottom: 3px solid black; padding-left:20px; padding-right:20px"}
{{< include ../README.md >}}
:::
